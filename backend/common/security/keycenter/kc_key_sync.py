# coding=utf-8

import base64
import json
import time
import urllib.error
import urllib.parse
import urllib.request

from . import KeyCenterError

_g_kc_server_host = ''
_g_publish_num = ''
_g_kc_key_dict = {}


def _sync_key_by_publish_num(publish_num):
    """
    fetch key data from keycenter server, using
    publish number(identifying an application on keycenter)
    :param publish_num:
    """
    global _g_kc_key_dict, _g_kc_server_host
    if not publish_num or not publish_num.replace(' ', ''):
        raise KeyCenterError('publish_num is empty')

    # construct request data
    current_time_stamp = str(int(time.time() * 1000))
    param_dict = dict({})
    param_dict["clientInfo"] = dict({})
    param_dict["data"] = dict({})
    param_dict["clientInfo"]["language"] = "Python"
    param_dict["clientInfo"]["version"] = "0.0.1"
    param_dict["clientInfo"]["timestamp"] = current_time_stamp
    param_dict["clientInfo"]["encrypt"] = False
    param_dict["data"]["appNum"] = publish_num
    param_dict["data"]["lastUpdatedTime"] = current_time_stamp
    param_base64 = base64.b64encode(json.dumps(param_dict).encode('utf-8')).decode('ascii')
    url = str(_g_kc_server_host)
    params = "action=queryApplication&data=" + param_base64
    req = urllib.request.Request(url, params.encode())
    response = urllib.request.urlopen(req)
    decoded = json.loads(response.read())

    if decoded["status"] != 'ok':
        return False
    response_data = decoded["data"]
    if response_data["status"] != 'ok':
        return False
    app_object = response_data["object"]
    key_ref_map = app_object["keyRefMap"]
    for item in key_ref_map:
        key_name = key_ref_map[item]["name"]
        key_version = key_ref_map[item]["keyHead"]["currentVersion"]
        key_detail = key_ref_map[item]["keyHead"]["keyVersionList"][key_version]
        content = key_detail["content"]
        key_detail["content"] = base64.b64decode(content)
        working_meta_json = key_detail["workingMetaJson"]
        try:
            key_detail["workingMetaJson"] = json.loads(working_meta_json)
        except Exception as msg:
            print(msg)
        generation_meta_json = key_detail["generationMetaJson"]
        try:
            key_detail["generationMetaJson"] = json.loads(generation_meta_json)
        except Exception as msg:
            print(msg)
        _g_kc_key_dict[key_name] = key_detail

    return True


def initialize(server_host, publish_num):
    """
    init this module, mainly read config info, and fetch key data from keycenter server
    :param server_host:
    :param publish_num:
    """
    global _g_kc_server_host, _g_publish_num
    _g_kc_server_host = server_host
    _g_publish_num = publish_num
    _sync_key_by_publish_num(_g_publish_num)


def get_key_dict():
    return _g_kc_key_dict
