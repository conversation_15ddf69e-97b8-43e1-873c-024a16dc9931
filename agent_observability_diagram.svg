<svg width="1600" height="1200" viewBox="0 0 1600 1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 现代渐变背景 -->
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff"/>
      <stop offset="50%" stop-color="#f8fafc"/>
      <stop offset="100%" stop-color="#e2e8f0"/>
    </linearGradient>

    <!-- 组件渐变 -->
    <linearGradient id="web-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#dbeafe"/>
      <stop offset="100%" stop-color="#bfdbfe"/>
    </linearGradient>

    <linearGradient id="gateway-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e0e7ff"/>
      <stop offset="100%" stop-color="#c7d2fe"/>
    </linearGradient>

    <linearGradient id="queue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fef3c7"/>
      <stop offset="100%" stop-color="#fde68a"/>
    </linearGradient>

    <linearGradient id="worker-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#d1fae5"/>
      <stop offset="100%" stop-color="#a7f3d0"/>
    </linearGradient>

    <linearGradient id="db-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f3e8ff"/>
      <stop offset="100%" stop-color="#e9d5ff"/>
    </linearGradient>

    <linearGradient id="highlight-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fef9c3"/>
      <stop offset="100%" stop-color="#fef08a"/>
    </linearGradient>

    <!-- 高级阴影效果 -->
    <filter id="card-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
      <feOffset dx="0" dy="6" result="offset"/>
      <feFlood flood-color="#1e293b" flood-opacity="0.1"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <filter id="glow-effect" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#6366f1"/>
    </marker>

    <marker id="arrowhead-green" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#10b981"/>
    </marker>

    <!-- 现代图标设计 -->
    <g id="icon-users">
      <circle cx="24" cy="24" r="20" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
      <path d="M16 32c0-4.4 3.6-8 8-8s8 3.6 8 8" stroke="#3b82f6" stroke-width="2" fill="none"/>
      <circle cx="24" cy="18" r="4" fill="#3b82f6"/>
      <circle cx="32" cy="16" r="3" fill="#3b82f6" opacity="0.6"/>
    </g>

    <g id="icon-gateway">
      <rect x="8" y="16" width="32" height="16" rx="4" fill="#e0e7ff" stroke="#6366f1" stroke-width="2"/>
      <path d="M16 24h16M20 20l4 4 4-4M20 28l4-4 4 4" stroke="#6366f1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>

    <g id="icon-web">
      <rect x="8" y="12" width="32" height="24" rx="4" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
      <rect x="12" y="16" width="24" height="2" fill="#3b82f6"/>
      <circle cx="14" cy="22" r="1" fill="#3b82f6"/>
      <circle cx="18" cy="22" r="1" fill="#3b82f6"/>
      <circle cx="22" cy="22" r="1" fill="#3b82f6"/>
      <path d="M12 26h24M12 30h16" stroke="#3b82f6" stroke-width="1.5"/>
    </g>

    <g id="icon-queue">
      <rect x="8" y="16" width="32" height="16" rx="4" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
      <path d="M14 20h4M14 24h6M14 28h8" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
      <circle cx="32" cy="20" r="2" fill="#f59e0b"/>
      <circle cx="34" cy="24" r="2" fill="#f59e0b"/>
      <circle cx="36" cy="28" r="2" fill="#f59e0b"/>
    </g>

    <g id="icon-worker">
      <circle cx="24" cy="24" r="20" fill="#d1fae5" stroke="#10b981" stroke-width="2"/>
      <path d="M24 16v8l6 4" stroke="#10b981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="24" cy="24" r="2" fill="#10b981"/>
    </g>

    <g id="icon-clickhouse">
      <rect x="8" y="16" width="32" height="16" rx="4" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
      <path d="M16 20h4v8h-4zM22 18h4v10h-4zM28 22h4v6h-4z" fill="#8b5cf6"/>
    </g>

    <g id="icon-postgres">
      <circle cx="24" cy="24" r="20" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
      <path d="M18 16h12v16H18z" fill="none" stroke="#8b5cf6" stroke-width="2"/>
      <path d="M22 20h8M22 24h6M22 28h8" stroke="#8b5cf6" stroke-width="1.5"/>
    </g>

    <g id="icon-oss">
      <rect x="8" y="16" width="32" height="16" rx="4" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2"/>
      <path d="M16 20l8 4 8-4M16 24l8 4 8-4M16 28l8 4 8-4" stroke="#8b5cf6" stroke-width="2" fill="none"/>
    </g>

    <g id="icon-highlight">
      <circle cx="12" cy="12" r="8" fill="#fef08a" stroke="#eab308" stroke-width="2"/>
      <path d="M12 8v8M8 12h8" stroke="#eab308" stroke-width="2" stroke-linecap="round"/>
    </g>

    <!-- 现代样式系统 -->
    <style>
      .font-inter {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
      }
      .main-title {
        font-size: 42px;
        font-weight: 800;
        fill: #0f172a;
        text-anchor: middle;
        letter-spacing: -0.02em;
      }
      .subtitle {
        font-size: 18px;
        font-weight: 500;
        fill: #64748b;
        text-anchor: middle;
      }
      .section-title {
        font-size: 24px;
        font-weight: 700;
        fill: #1e293b;
        text-anchor: middle;
      }
      .component-title {
        font-size: 18px;
        font-weight: 600;
        fill: #1e293b;
      }
      .component-subtitle {
        font-size: 14px;
        font-weight: 500;
        fill: #64748b;
      }
      .tech-highlight {
        font-size: 12px;
        font-weight: 600;
        fill: #eab308;
      }
      .description-text {
        font-size: 14px;
        fill: #475569;
        line-height: 1.5;
      }
      .highlight-text {
        font-size: 16px;
        font-weight: 700;
        fill: #059669;
      }
      .card {
        filter: url(#card-shadow);
        rx: 16;
        ry: 16;
      }
      .connector {
        stroke: #6366f1;
        stroke-width: 3;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .connector-data {
        stroke: #10b981;
        stroke-width: 2;
        fill: none;
        marker-end: url(#arrowhead-green);
        stroke-dasharray: 5,5;
      }
    </style>

    <!-- 流程箭头 -->
    <marker id="arrow-flow" markerWidth="14" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 14 5, 0 10" fill="#6366f1"/>
    </marker>
  </defs>

  <!-- 现代背景 -->
  <rect width="1800" height="1200" fill="url(#bg-gradient)"/>

  <!-- 主标题区域 -->
  <text x="900" y="80" class="hero-title">Agent 智能体可观测性挑战</text>
  <text x="900" y="120" class="hero-subtitle">传统监控工具面临的三大核心困境</text>

  <!-- 顶部三个关键点 -->
  <g transform="translate(150, 160)">
    <!-- 关键点1：传统日志局限 -->
    <g transform="translate(0, 0)">
      <rect class="modern-card" width="450" height="120" fill="url(#keypoint-bg)" stroke="#e2e8f0" stroke-width="2"/>
      <use href="#icon-logs" transform="translate(30, 30) scale(1.2)"/>
      <text x="225" y="35" class="keypoint-title">❌ 传统日志 &amp; Traces 失效</text>
      <text x="225" y="60" class="keypoint-text">只能记录API调用和错误信息</text>
      <text x="225" y="80" class="keypoint-text">无法捕获Agent的思考和决策过程</text>
      <text x="225" y="100" class="keypoint-text">缺失关键的"为什么"和"怎么想"</text>
    </g>

    <!-- 关键点2：黑盒困境 -->
    <g transform="translate(500, 0)">
      <rect class="modern-card" width="450" height="120" fill="url(#keypoint-bg)" stroke="#e2e8f0" stroke-width="2"/>
      <use href="#icon-blackbox" transform="translate(30, 30) scale(1.2)"/>
      <text x="225" y="35" class="keypoint-title">⚠️ Agent 内部完全黑盒</text>
      <text x="225" y="60" class="keypoint-text">推理链路不可见，调试如盲人摸象</text>
      <text x="225" y="80" class="keypoint-text">成本消耗不透明，优化无从下手</text>
      <text x="225" y="100" class="keypoint-text">性能瓶颈难定位，问题复现困难</text>
    </g>

    <!-- 关键点3：缺乏量化 -->
    <g transform="translate(1000, 0)">
      <rect class="modern-card" width="450" height="120" fill="url(#keypoint-bg)" stroke="#e2e8f0" stroke-width="2"/>
      <use href="#icon-challenge" transform="translate(30, 30) scale(1.2)"/>
      <text x="225" y="35" class="keypoint-title">📊 缺乏量化评估体系</text>
      <text x="225" y="60" class="keypoint-text">无法衡量Agent表现好坏</text>
      <text x="225" y="80" class="keypoint-text">版本迭代缺乏数据支撑</text>
      <text x="225" y="100" class="keypoint-text">ROI计算和成本控制困难</text>
    </g>
  </g>

  <!-- 主要内容区域：三列布局 -->
  <g transform="translate(100, 320)">

    <!-- 第一列：传统日志和Traces的局限性 -->
    <g transform="translate(0, 0)">
      <rect class="modern-card glow-red" width="500" height="800" fill="url(#traditional-bg)" stroke="#f87171" stroke-width="3"/>
      <text x="250" y="50" class="section-title traditional-title">传统监控工具局限</text>

      <!-- 传统日志问题 -->
      <g transform="translate(40, 100)">
        <rect class="modern-card" width="420" height="200" fill="white" stroke="#fca5a5" stroke-width="2"/>
        <use href="#icon-logs" transform="translate(30, 30) scale(1.5)"/>
        <text x="100" y="50" class="card-title highlight-critical">📋 传统日志系统</text>
        <text x="30" y="80" class="card-text">• 只记录API调用和错误信息</text>
        <text x="30" y="105" class="card-text">• 无法捕获Agent思考过程</text>
        <text x="30" y="130" class="card-text">• 缺失决策链路和推理逻辑</text>
        <text x="30" y="155" class="card-text highlight-critical">• 问题定位如大海捞针</text>
        <text x="30" y="180" class="card-text highlight-critical">• 调试周期长，效率低下</text>
      </g>

      <!-- 传统Traces问题 -->
      <g transform="translate(40, 330)">
        <rect class="modern-card" width="420" height="200" fill="white" stroke="#fca5a5" stroke-width="2"/>
        <use href="#icon-traces" transform="translate(30, 30) scale(1.5)"/>
        <text x="100" y="50" class="card-title highlight-critical">🔗 传统链路追踪</text>
        <text x="30" y="80" class="card-text">• 只能追踪服务间调用关系</text>
        <text x="30" y="105" class="card-text">• Agent内部推理链路断裂</text>
        <text x="30" y="130" class="card-text">• 无法展现"思考→计划→执行"</text>
        <text x="30" y="155" class="card-text highlight-critical">• 智能决策过程完全黑盒</text>
        <text x="30" y="180" class="card-text highlight-critical">• 性能瓶颈难以定位</text>
      </g>

      <!-- 核心问题总结 -->
      <g transform="translate(40, 560)">
        <rect class="modern-card" width="420" height="180" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
        <text x="210" y="35" class="emphasis-text highlight-critical">核心问题</text>
        <text x="30" y="70" class="card-text">传统工具面对Agent时：</text>
        <text x="30" y="95" class="card-text highlight-critical">❌ 看不到"为什么这样思考"</text>
        <text x="30" y="120" class="card-text highlight-critical">❌ 追不到"如何做决策"</text>
        <text x="30" y="145" class="card-text highlight-critical">❌ 控不住"成本和性能"</text>
        <text x="210" y="170" class="emphasis-text highlight-critical">结果 = 昂贵的智能黑盒</text>
      </g>
    </g>

    <!-- 流程箭头 -->
    <path d="M 520 400 Q 580 400 620 400" stroke="#6366f1" stroke-width="4" fill="none" marker-end="url(#arrow-flow)"/>

    <!-- 第二列：Agent面临的核心挑战 -->
    <g transform="translate(650, 0)">
      <rect class="modern-card" width="500" height="800" fill="url(#challenge-bg)" stroke="#fb923c" stroke-width="3"/>
      <text x="250" y="50" class="section-title challenge-title">Agent 核心挑战</text>

      <!-- 黑盒可视化 -->
      <g transform="translate(150, 100)">
        <rect width="200" height="150" rx="20" fill="#1f2937" stroke="#374151" stroke-width="3" filter="url(#card-shadow)"/>
        <use href="#icon-blackbox" transform="translate(70, 45) scale(2)"/>
        <text x="100" y="120" fill="#9ca3af" text-anchor="middle" font-size="16" font-weight="600">Agent 黑盒</text>
        <text x="100" y="140" fill="#6b7280" text-anchor="middle" font-size="14">不可观测</text>
      </g>

      <!-- 挑战列表 -->
      <g transform="translate(40, 280)">
        <rect class="modern-card" width="420" height="450" fill="white" stroke="#fb923c" stroke-width="2"/>

        <!-- 挑战1 -->
        <g transform="translate(30, 40)">
          <use href="#icon-challenge" transform="scale(0.8)"/>
          <text x="50" y="25" class="card-title highlight-warning">🚨 调试噩梦</text>
          <text x="30" y="50" class="card-text">• 错误发生时无法定位根因</text>
          <text x="30" y="75" class="card-text">• 推理过程不透明</text>
          <text x="30" y="100" class="card-text highlight-warning">• 修复时间成倍增长</text>
        </g>

        <!-- 挑战2 -->
        <g transform="translate(30, 150)">
          <circle cx="20" cy="20" r="16" fill="#ea580c" opacity="0.2" stroke="#ea580c" stroke-width="2"/>
          <text x="20" y="26" fill="#ea580c" text-anchor="middle" font-size="16" font-weight="bold">$</text>
          <text x="50" y="25" class="card-title highlight-warning">💰 成本失控</text>
          <text x="30" y="50" class="card-text">• Token消耗不可预测</text>
          <text x="30" y="75" class="card-text">• 延迟累积严重</text>
          <text x="30" y="100" class="card-text highlight-warning">• ROI无法计算</text>
        </g>

        <!-- 挑战3 -->
        <g transform="translate(30, 260)">
          <circle cx="20" cy="20" r="16" fill="#ea580c" opacity="0.2" stroke="#ea580c" stroke-width="2"/>
          <text x="20" y="26" fill="#ea580c" text-anchor="middle" font-size="16" font-weight="bold">?</text>
          <text x="50" y="25" class="card-title highlight-warning">📈 优化盲区</text>
          <text x="30" y="50" class="card-text">• 无法量化性能表现</text>
          <text x="30" y="75" class="card-text">• 版本对比困难</text>
          <text x="30" y="100" class="card-text highlight-warning">• 优化方向靠猜测</text>
        </g>

        <!-- 挑战总结 -->
        <g transform="translate(30, 370)">
          <rect width="360" height="50" rx="12" fill="#fff7ed" stroke="#fb923c" stroke-width="1"/>
          <text x="180" y="32" class="emphasis-text highlight-warning" text-anchor="middle">Agent = 不可控的智能黑盒</text>
        </g>
      </g>
    </g>

    <!-- 流程箭头 -->
    <path d="M 1170 400 Q 1230 400 1270 400" stroke="#6366f1" stroke-width="4" fill="none" marker-end="url(#arrow-flow)"/>

    <!-- 第三列：Agent Trace解决方案 -->
    <g transform="translate(1300, 0)">
      <rect class="modern-card glow-green" width="500" height="800" fill="url(#solution-bg)" stroke="#34d399" stroke-width="3"/>
      <text x="250" y="50" class="section-title solution-title">Agent Trace 解决方案</text>

      <!-- 透明可视化 -->
      <g transform="translate(150, 100)">
        <rect width="200" height="150" rx="20" fill="white" stroke="#10b981" stroke-width="3" filter="url(#card-shadow)"/>
        <use href="#icon-agent-trace" transform="translate(70, 45) scale(2)"/>
        <text x="100" y="120" fill="#059669" text-anchor="middle" font-size="16" font-weight="600">Agent 透明</text>
        <text x="100" y="140" fill="#10b981" text-anchor="middle" font-size="14">完全可观测</text>
      </g>

      <!-- 解决方案列表 -->
      <g transform="translate(40, 280)">
        <rect class="modern-card" width="420" height="450" fill="white" stroke="#34d399" stroke-width="2"/>

        <!-- 解决方案1 -->
        <g transform="translate(30, 40)">
          <use href="#icon-solution" transform="scale(0.8)"/>
          <text x="50" y="25" class="card-title highlight-success">🎯 精准调试</text>
          <text x="30" y="50" class="card-text">• 完整记录思考→计划→执行</text>
          <text x="30" y="75" class="card-text">• 每步都可追溯和回放</text>
          <text x="30" y="100" class="card-text highlight-success">• 问题秒级定位</text>
        </g>

        <!-- 解决方案2 -->
        <g transform="translate(30, 150)">
          <circle cx="20" cy="20" r="16" fill="#10b981" opacity="0.2" stroke="#10b981" stroke-width="2"/>
          <text x="20" y="26" fill="#10b981" text-anchor="middle" font-size="16" font-weight="bold">✓</text>
          <text x="50" y="25" class="card-title highlight-success">💎 成本透明</text>
          <text x="30" y="50" class="card-text">• 实时Token消耗监控</text>
          <text x="30" y="75" class="card-text">• 性能热点识别</text>
          <text x="30" y="100" class="card-text highlight-success">• 精确成本控制</text>
        </g>

        <!-- 解决方案3 -->
        <g transform="translate(30, 260)">
          <circle cx="20" cy="20" r="16" fill="#10b981" opacity="0.2" stroke="#10b981" stroke-width="2"/>
          <text x="20" y="26" fill="#10b981" text-anchor="middle" font-size="16" font-weight="bold">📊</text>
          <text x="50" y="25" class="card-title highlight-success">📈 数据驱动</text>
          <text x="30" y="50" class="card-text">• 多维度性能指标</text>
          <text x="30" y="75" class="card-text">• A/B测试支持</text>
          <text x="30" y="100" class="card-text highlight-success">• 持续优化迭代</text>
        </g>

        <!-- 解决方案总结 -->
        <g transform="translate(30, 370)">
          <rect width="360" height="50" rx="12" fill="#f0fdf4" stroke="#34d399" stroke-width="1"/>
          <text x="180" y="32" class="emphasis-text highlight-success" text-anchor="middle">Agent = 可信赖的智能助手</text>
        </g>
      </g>
    </g>
  </g>

  <!-- 底部价值主张 -->
  <g transform="translate(300, 1150)">
    <rect width="1200" height="60" rx="30" fill="white" stroke="#6366f1" stroke-width="3" filter="url(#card-shadow)"/>
    <text x="600" y="40" class="hero-title" style="font-size: 28px; fill: #6366f1;" text-anchor="middle">🚀 Agent Trace：让AI从黑盒变透明，从猜测变科学，从成本黑洞变精确控制</text>
  </g>

</svg>

